networks:
  nextya:

services:
  app:
    container_name: nextya_app
    build:
      context: .
      dockerfile: docker/app.dockerfile
      target: development
      args:
        - USER_ID=${USER_ID:-1000}
        - GROUP_ID=${GROUP_ID:-1000}
    ports:
      - '5173:5173'
    environment:
      - DB_HOST=postgres
      - DB_USER=postgres
      - DB_PASSWORD=postgres
      - DB_NAME=nextya
      - DB_PORT=5432
      - NODE_ENV=development
    volumes:
      - .:/app:cached
      - node_modules_cache:/app/node_modules
    depends_on:
      - nextya_postgres
    networks:
      - nextya
    stdin_open: true
    tty: true

  nextya_postgres:
    container_name: nextya_postgres
    image: postgres:14-alpine
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_DB=nextya
    volumes:
      - ./docker/init:/docker-entrypoint-initdb.d
      - postgres_data:/var/lib/postgresql/data
    ports:
      - '5432:5432'
    networks:
      - nextya
    healthcheck:
      test: ['CMD-SHELL', 'pg_isready -U postgres']
      interval: 10s
      timeout: 5s
      retries: 5

volumes:
  postgres_data:
  node_modules_cache: