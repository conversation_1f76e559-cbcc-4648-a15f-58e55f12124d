<script lang="ts">
	import type { FileEntry } from '$lib/types';
	import { Play, X, Loader2, Eye, Edit, Check, AlertCircle } from 'lucide-svelte';

	type Props = {
		entries: FileEntry[];
		selectedId: string | null;
		processingId: string | null;
		validationErrorsMap: Map<string, string>;
		evalSelected: boolean;
		onSelect: (id: string) => void;
		onProcess: (id: string, rollCode?: string) => void;
		onRemove: (id: string) => void;
		onViewDetails: (id: string) => void;
		onUpdateRollCode: (id: string, newRollCode: string) => void;
	};

	// Constante para validación de código de matrícula
	const ROLL_CODE_PATTERN = /^\d{4}$/;

	const {
		entries = [],
		selectedId = null,
		processingId = null,
		validationErrorsMap = new Map(),
		evalSelected = false,
		onSelect = () => {},
		onProcess = () => {},
		onRemove = () => {},
		onViewDetails = () => {},
		onUpdateRollCode = () => {}
	}: Props = $props();

	let editingId = $state<string | null>(null);
	let editedRollCode = $state('');
	let tableContainer = $state<HTMLDivElement | null>(null);
	let rollCodeInput = $state<HTMLInputElement | null>(null);

	// Function to safely reset editing state
	function resetEditingState() {
		editingId = null;
		editedRollCode = '';

		// Focus the table container immediately
		if (tableContainer) tableContainer.focus();
	}

	function startEditing(id: string, initialCode: string, event: MouseEvent) {
		event.stopPropagation();
		if (editingId !== null) return;

		// Check if the entry still exists
		if (!entries.some((entry) => entry.id === id)) return;

		editingId = id;
		editedRollCode = initialCode;

		// Focus the input field on the next tick using $effect
		$effect(() => {
			if (editingId === id && rollCodeInput) rollCodeInput.focus();
		});
	}

	function handleKeyDown(event: KeyboardEvent) {
		// Handle Escape key to cancel editing
		if (event.key === 'Escape' && editingId !== null) {
			resetEditingState();
			event.preventDefault();
			return;
		}

		// Don't navigate while editing
		if (editingId !== null) return;

		// Only handle arrow keys for navigation
		if (event.key !== 'ArrowUp' && event.key !== 'ArrowDown') return;

		event.preventDefault(); // Prevent page scrolling

		// Find current index
		const currentIndex = entries.findIndex((entry) => entry.id === selectedId);
		if (currentIndex === -1) return;

		// Calculate new index based on arrow key
		const isArrowUp = event.key === 'ArrowUp';
		const newIndex = isArrowUp
			? Math.max(0, currentIndex - 1)
			: Math.min(entries.length - 1, currentIndex + 1);

		// Only update if index changed
		if (newIndex !== currentIndex) {
			onSelect(entries[newIndex].id);
		}
	}

	function confirmEdit(id: string, event: Event) {
		event.stopPropagation();
		if (editedRollCode && ROLL_CODE_PATTERN.test(editedRollCode)) {
			onUpdateRollCode(id, editedRollCode);
		}
		resetEditingState();
	}

	function cancelEdit(event: Event) {
		event.stopPropagation();
		resetEditingState();
	}

	// Combined status function to reduce redundant checks
	function getStatus(entry: FileEntry): {
		icon: typeof AlertCircle;
		color: string;
		pulse?: boolean;
	} {
		// Processing state
		const isProcessing = entry.id === processingId || entry.status === 'processing';
		if (isProcessing) {
			return { icon: Loader2, color: 'text-info animate-spin', pulse: true };
		}

		// Error states
		const hasValidationError = validationErrorsMap.has(entry.id);
		const hasFormatError = !entry.formatValid;
		const hasProcessError = entry.status === 'error';

		if (hasValidationError || hasFormatError || hasProcessError) {
			return { icon: AlertCircle, color: 'text-error' };
		}

		// Warning state (student not found)
		const isSuccessWithoutRegister = entry.status === 'success' && !entry.result?.register_code;
		if (isSuccessWithoutRegister) {
			return { icon: AlertCircle, color: 'text-warning' };
		}

		// Success state
		if (entry.status === 'success') {
			return { icon: Check, color: 'text-success' };
		}

		// Pending state (default)
		return { icon: AlertCircle, color: 'text-base-content/50' };
	}

	function getScoreDisplay(entry: FileEntry): { text: string; class: string } {
		if (entry.status === 'success' && entry.result?.scores?.general) {
			const score = entry.result.scores.general.score;
			return { text: score.toFixed(1), class: 'font-bold' };
		}
		return { text: '-', class: 'text-base-content/50' };
	}

	function getTooltip(entry: FileEntry): string | null {
		// Use a priority-based approach with early returns
		const validationError = validationErrorsMap.get(entry.id);
		if (validationError) return validationError;

		if (!entry.formatValid) return `Formato no A5: ${entry.formatName}.`;

		const isProcessing = entry.id === processingId || entry.status === 'processing';
		if (isProcessing) return 'Procesando...';

		// Status-based messages
		switch (entry.status) {
			case 'error':
				return entry.error?.message ?? 'Error desconocido';
			case 'success':
				if (!entry.result?.register_code) {
					return `Estudiante con código ${entry.result?.roll_code} no encontrado en registros.`;
				}
				return entry.saved ? 'Resultado guardado en la base de datos.' : 'Procesado correctamente.';
			case 'pending':
				return 'Pendiente de procesamiento.';
			default:
				return null;
		}
	}
</script>

{#snippet statusIcon(entry: FileEntry)}
	{@const status = getStatus(entry)}
	{@const tooltip = getTooltip(entry)}
	<div class="tooltip tooltip-right">
		<div class="tooltip-content">
			<div class="max-w-[6rem]">{tooltip}</div>
		</div>
		<div class={status.pulse ? 'animate-pulse' : ''}>
			<status.icon size={16} class={status.color} />
		</div>
	</div>
{/snippet}
{#snippet rollCodeEditor(entry: FileEntry)}
	<div class="join h-7">
		<input
			type="text"
			class="join-item input input-bordered input-xs w-16 px-2 font-mono {editedRollCode &&
			!ROLL_CODE_PATTERN.test(editedRollCode)
				? 'input-error'
				: 'input-primary'}"
			bind:value={editedRollCode}
			bind:this={rollCodeInput}
			pattern="\d{4}"
			maxlength="4"
			placeholder="0000"
			aria-label="Editar código de matrícula"
			onkeydown={(e) => {
				if (e.key === 'Escape') cancelEdit(e);
				if (e.key === 'Enter' && editedRollCode && ROLL_CODE_PATTERN.test(editedRollCode)) {
					confirmEdit(entry.id, e);
				}
			}}
		/>
		<button
			class="join-item btn btn-primary btn-xs btn-square"
			onclick={(e) => confirmEdit(entry.id, e)}
			disabled={!editedRollCode || !ROLL_CODE_PATTERN.test(editedRollCode)}
			title="Confirmar y Reprocesar"
			aria-label="Confirmar y Reprocesar código"
		>
			<Check size={14} />
		</button>
		<button
			class="join-item btn btn-ghost btn-xs btn-square"
			onclick={cancelEdit}
			title="Cancelar edición"
			aria-label="Cancelar edición de código"
		>
			<X size={14} />
		</button>
	</div>
{/snippet}
{#snippet rollCodeDisplay(entry: FileEntry, isBusy: boolean)}
	<div class="flex items-center gap-1 h-7">
		{#if entry.result?.roll_code || entry.error?.roll_code}
			<code>
				{entry.result?.roll_code || entry.error?.roll_code}
			</code>
			<button
				class="btn btn-ghost btn-xs btn-square text-base-content/60 hover:text-primary"
				onclick={(e) =>
					startEditing(entry.id, entry.result?.roll_code || entry.error?.roll_code || '', e)}
				title="Editar código y reprocesar"
				aria-label="Editar código y reprocesar"
				disabled={isBusy}
			>
				<Edit size={12} />
			</button>
		{:else if entry.status === 'pending' || (entry.status === 'error' && !entry.error?.roll_code)}
			<button
				class="btn btn-outline btn-xs btn-primary"
				onclick={(e) => startEditing(entry.id, '', e)}
				disabled={isBusy}
				title="Especificar código de 4 dígitos para procesar"
				aria-label="Asignar código de matrícula"
			>
				<Edit size={12} class="mr-0.5" /> Asignar
			</button>
		{:else}
			<span class="text-xs opacity-50">-</span>
		{/if}
	</div>
{/snippet}
<div
	class="max-h-[50rem] overflow-auto rounded-lg bg-base-200/50 focus:outline-none focus:ring-2 focus:ring-primary/50"
	tabindex="0"
	onkeydown={handleKeyDown}
	role="grid"
	aria-label="Tabla de archivos"
	bind:this={tableContainer}
>
	<div class="text-xs text-right text-base-content/40 px-2 pt-1">
		<kbd class="kbd kbd-xs">↑</kbd> <kbd class="kbd kbd-xs">↓</kbd> para navegar
	</div>
	<table class="table table-sm table-pin-rows w-full">
		<thead>
			<tr>
				<th class="w-10"></th>
				<th>Archivo</th>
				<th>Código</th>
				<th>Estudiante</th>
				<th class="text-center">Nota</th>
				<th class="text-right pr-4">Acciones</th>
			</tr>
		</thead>
		<tbody>
			{#each entries as entry (entry.id)}
				{@const scoreInfo = getScoreDisplay(entry)}
				{@const isProcessing = entry.id === processingId || entry.status === 'processing'}
				{@const isEditingOther = editingId !== null && editingId !== entry.id}
				{@const isBusy = isProcessing || isEditingOther}
				<tr
					class={[
						'cursor-pointer hover:bg-primary/10',
						selectedId === entry.id && 'bg-primary/10 outline outline-primary/30'
					]}
					onclick={() => {
						// Check if we're currently editing and need to reset
						if (editingId !== null && editingId !== entry.id) {
							resetEditingState();
						}
						onSelect(entry.id);
					}}
				>
					<td class="text-center pl-2">
						{@render statusIcon(entry)}
					</td>
					<td class="truncate max-w-[10rem] py-2.5" title={entry.file.name}>
						{entry.file.name}
					</td>
					<td class="font-mono text-sm">
						{#if editingId === entry.id}
							{@render rollCodeEditor(entry)}
						{:else}
							{@render rollCodeDisplay(entry, isBusy)}
						{/if}
					</td>
					<td class="truncate max-w-xs text-sm">
						{#if entry.result?.student}
							<span
								class="flex items-center gap-1.5"
								title={`${entry.result.student.name} ${entry.result.student.last_name}`}
							>
								<Check size={14} class="text-success flex-shrink-0" />
								{entry.result.student.name}
								{entry.result.student.last_name}
							</span>
						{:else if entry.status === 'success' && entry.result && !entry.result.student}
							<span
								class="flex items-center gap-1.5 text-warning"
								title={`Estudiante ${entry.result.roll_code} no encontrado`}
							>
								No encontrado
							</span>
						{:else}
							<span class="text-xs opacity-50">-</span>
						{/if}
					</td>
					<td class="text-center">
						<span class={scoreInfo.class}>{scoreInfo.text}</span>
					</td>
					<td class="text-right pr-4">
						<div class="flex gap-1 justify-end items-center h-7">
							{#if (entry.status === 'pending' || entry.status === 'error') && editingId !== entry.id}
								<button
									class="btn btn-xs btn-primary"
									onclick={() => onProcess(entry.id)}
									disabled={!evalSelected || isBusy || !entry.formatValid}
									title={!entry.formatValid ? 'Formato no A5' : 'Procesar'}
								>
									{#if isProcessing}
										<Loader2 size={14} class="animate-spin" />
									{:else}
										<Play size={14} />
									{/if}
								</button>
							{/if}
							{#if entry.status === 'success'}
								<button
									class="btn btn-ghost btn-xs btn-square"
									onclick={() => onViewDetails(entry.id)}
									title="Ver detalles de respuestas"
									aria-label="Ver detalles de respuestas"
									disabled={isEditingOther}
								>
									<Eye size={14} />
								</button>
							{/if}
							<button
								class="btn btn-ghost btn-error btn-xs btn-square"
								onclick={() => {
									// Reset editing state if removing the item being edited
									if (editingId === entry.id) {
										resetEditingState();
									}
									onRemove(entry.id);
								}}
								disabled={isBusy}
								title="Eliminar archivo"
								aria-label="Eliminar archivo"
							>
								<X size={14} />
							</button>
						</div>
					</td>
				</tr>
			{:else}
				<tr>
					<td colspan="6" class="text-center py-8 opacity-50">
						No hay archivos cargados. Añade imágenes para comenzar.
					</td>
				</tr>
			{/each}
		</tbody>
	</table>
</div>
